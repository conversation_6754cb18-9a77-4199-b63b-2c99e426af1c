{"name": "hugging-zkt-frontend", "version": "1.0.0", "description": "HuggingZKT Platform Frontend with Ant Design Pro & Umi4", "private": true, "scripts": {"build": "umi build", "dev": "umi dev", "format": "prettier --cache --write .", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev", "lint": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "type-check": "tsc --noEmit"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.6.43", "@ant-design/pro-layout": "^7.17.16", "@umijs/max": "^4.0.87", "antd": "^5.12.8", "classnames": "^2.3.2", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "umi": "^4.0.87"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "typescript": "^5.3.3"}, "engines": {"node": ">=16.0.0"}}