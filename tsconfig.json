{"extends": "./src/.umi/tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"]}}, "include": ["mock/**/*", "src/**/*", "config/**/*", ".umirc.ts", "typings.d.ts"], "exclude": ["node_modules", "lib", "es", "dist", "typings", "**/*.d.ts"]}