import { defineConfig } from '@umijs/max';

export default defineConfig({
  antd: {
    // 配置 antd 主题
    theme: {
      token: {
        colorPrimary: '#1890ff',
      },
    },
  },
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    title: 'HuggingZKT Platform',
    locale: false,
  },
  routes: [
    {
      path: '/user',
      layout: false,
      routes: [
        {
          name: 'login',
          path: '/user/login',
          component: './User/Login',
        },
        {
          name: 'register',
          path: '/user/register',
          component: './User/Register',
        },
        {
          name: 'forgot-password',
          path: '/user/forgot-password',
          component: './User/ForgotPassword',
        },
        {
          name: 'reset-password',
          path: '/user/reset-password',
          component: './User/ResetPassword',
        },
      ],
    },
    {
      path: '/welcome',
      name: 'welcome',
      icon: 'smile',
      component: './Welcome',
    },
    {
      path: '/',
      redirect: '/welcome',
    },
    {
      path: '*',
      layout: false,
      component: './404',
    },
  ],
  npmClient: 'npm',
  tailwindcss: {},
  // 配置代理
  proxy: {
    '/api': {
      target: 'http://localhost:8000',
      changeOrigin: true,
      pathRewrite: { '^/api': '/api' },
    },
  },
  // 配置别名
  alias: {
    '@': require('path').resolve(__dirname, 'src'),
  },
  // 开启 hash 模式
  hash: true,
  // 配置 less
  lessLoader: {
    modifyVars: {
      '@primary-color': '#1890ff',
    },
    javascriptEnabled: true,
  },
  // 配置 CSS Modules
  cssModulesTypescriptLoader: {
    mode: 'verify',
  },
});
