import React from 'react';
import { Outlet } from 'umi';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import styles from './UserLayout.module.less';

const UserLayout: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <div className={styles.container}>
        <div className={styles.content}>
          <Outlet />
        </div>
      </div>
    </ConfigProvider>
  );
};

export default UserLayout;
